# SalesMasterPro2025

برنامج مبيعات احترافي بواجهة رسومية باستخدام Python وTkinter.

## الميزات:
- إدارة المخزون (إضافة/تعديل/حذف/بحث المنتجات)
- إدارة العملاء
- نقطة بيع ذكية مع دعم الباركود
- تقارير المبيعات والأرباح
- إدارة مشتركين الانترنت
- الحفظ والنسخ الاحتياطي والاسترجاع
- إدارة الديون
- واجهة مستخدم عصرية بالكامل باللغة العربية

## المتطلبات:
- Python 3.8+
- مكتبة tkinter (موجودة افتراضيًا مع بايثون)

## التشغيل:
```bash
python main.py
```

## الحفظ والبيانات:
- جميع البيانات تحفظ تلقائيًا في ملف sales_data.json في مجلد البرنامج.
- يدعم النسخ الاحتياطي والاسترجاع من خلال الواجهة.

## المطور:
- علي حاتم
