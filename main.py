import tkinter as tk
from tkinter import ttk, messagebox, filedialog, colorchooser
import datetime
import json
import os
import csv
import tempfile

# إعداد نافذة البرنامج
root = tk.Tk()
root.title("SalesMasterPro2025 - برنامج مبيعات احترافي")
root.geometry("1100x700")
root.configure(bg="#f0f4f9")

# عنوان البرنامج
main_title_frame = tk.Frame(root, bg="#f0f4f9")
main_title_frame.pack(pady=(20, 0), fill="x")
title = tk.Label(main_title_frame, text="SalesMasterPro2025 - برنامج مبيعات احترافية", font=("Cairo", 30, "bold"), bg="#f0f4f9", fg="#273c75")
title.pack(side="right", padx=30)
subtitle = tk.Label(main_title_frame, text="المطور علي حاتم", font=("Cairo", 14), bg="#f0f4f9", fg="#353b48")
subtitle.pack(side="right", padx=10)

# إعداد التبويبات
style = ttk.Style()
style.theme_use('default')
style.configure('TNotebook.Tab', background='#487eb0', foreground='white', font=("Cairo", 18, "bold"), padding=[30, 15])
style.map('TNotebook.Tab', background=[('selected', '#e1b12c')], foreground=[('selected', '#2d3436')])
style.configure('TNotebook', tabposition='n')
notebook = ttk.Notebook(root, style='TNotebook')
notebook.pack(expand=1, fill='both', padx=40, pady=30)

# تعريف التبويبات
frames = []
tab_names = ["المخزون", "العملاء", "الإعدادات", "نقطة البيع", "التقارير", "مشتركين الانترنت", "الحفظ والنسخ الاحتياطي", "الديون"]
for name in tab_names:
    frame = tk.Frame(notebook, bg="#dff9fb")
    notebook.add(frame, text=name)
    frames.append(frame)

# متغيرات البيانات
products = []
sales_history = []
subscribers = []
debts = []
customers = []  # إضافة تعريف قائمة العملاء

# --- سجل العمليات (audit log) ---
def log_action(action, details=None):
    from datetime import datetime
    entry = {
        'user': 'غير معروف',
        'role': 'غير معروف',
        'action': action,
        'details': details or '',
        'date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    try:
        with open("audit_log.json", "a", encoding="utf-8") as f:
            f.write(json.dumps(entry, ensure_ascii=False) + "\n")
    except:
        pass

# --- نظام الإشعارات والتنبيهات الذكية ---
LOW_STOCK_LIMIT = 5  # الكمية التي عندها يظهر تنبيه انخفاض المخزون
EXPIRY_ALERT_DAYS = 7  # عدد الأيام قبل انتهاء الاشتراك لإظهار التنبيه

def check_notifications():
    alerts = []
    # تنبيه انخفاض المخزون
    for p in products:
        if int(p.get('qty', 0)) <= LOW_STOCK_LIMIT:
            alerts.append(f"تنبيه: المنتج '{p['name']}' الكمية منخفضة ({p['qty']})")
    # تنبيه انتهاء اشتراك الانترنت
    from datetime import datetime, timedelta
    today = datetime.now().date()
    for s in subscribers:
        try:
            expiry = datetime.strptime(s['expiry'], "%Y-%m-%d").date()
            if (expiry - today).days <= EXPIRY_ALERT_DAYS:
                alerts.append(f"تنبيه: اشتراك '{s['name']}' ينتهي خلال {(expiry - today).days} يوم")
        except:
            continue
    # تنبيه الديون المتأخرة (مثال: إذا كان هناك ملاحظة 'متأخر')
    for d in debts:
        try:
            if 'متأخر' in d.get('note', ''):
                alerts.append(f"تنبيه: دين متأخر على '{d['customer']}' بمبلغ {d['amount']}")
        except:
            continue
    if alerts:
        show_alerts(alerts)

def show_alerts(alerts):
    alert_win = tk.Toplevel(root)
    alert_win.title("تنبيهات النظام")
    alert_win.geometry("400x350")
    alert_win.resizable(False, False)
    tk.Label(alert_win, text="تنبيهات هامة", font=("Cairo", 18, "bold"), fg="#c23616").pack(pady=10)
    frame = tk.Frame(alert_win)
    frame.pack(fill="both", expand=1)
    for alert in alerts:
        tk.Label(frame, text=alert, font=("Cairo", 13), fg="#e84118", anchor="w", justify="right").pack(anchor="w", padx=10, pady=3)
    tk.Button(alert_win, text="إغلاق", command=alert_win.destroy, bg="#487eb0", fg="white", font=("Cairo", 12)).pack(pady=10)

# دوال الحفظ والتحميل
DATA_FILE = "sales_data.json"
def save_data():
    data = {
        'products': products,
        'sales_history': [dict(s, date=s['date'].strftime("%Y-%m-%d %H:%M:%S") if isinstance(s['date'], datetime.datetime) else s['date']) for s in sales_history],
        'subscribers': subscribers,
        'debts': debts
    }
    with open(DATA_FILE, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    messagebox.showinfo("تم الحفظ", "تم حفظ البيانات بنجاح!")
def load_data():
    if not os.path.exists(DATA_FILE):
        return
    try:
        with open(DATA_FILE, "r", encoding="utf-8") as f:
            data = json.load(f)
            products.clear(); products.extend(data.get('products', []))
            sales_history.clear()
            for s in data.get('sales_history', []):
                s_copy = s.copy()
                if isinstance(s_copy.get('date'), str):
                    try:
                        s_copy['date'] = datetime.datetime.strptime(s_copy['date'], "%Y-%m-%d %H:%M:%S")
                    except:
                        pass
                sales_history.append(s_copy)
            subscribers.clear(); subscribers.extend(data.get('subscribers', []))
            debts.clear(); debts.extend(data.get('debts', []))
    except Exception as e:
        messagebox.showerror("خطأ في التحميل", str(e))

load_data()

# --- تبويب المخزون ---
if tab_names[0] == "المخزون":
    frame = frames[0]
    # حقل البحث
    search_var = tk.StringVar()
    def search_products(*args):
        query = search_var.get().strip().lower()
        tree.delete(*tree.get_children())
        for idx, p in enumerate(products, 1):
            if query in str(p['name']).lower() or query in str(p['barcode']).lower():
                tree.insert("", "end", values=(idx, p['name'], p['buy'], p['sell'], p['profit'], p['barcode'], p['qty']))
    search_frame = tk.Frame(frame, bg="#dff9fb")
    search_frame.pack(pady=(10,0))
    tk.Label(search_frame, text="بحث:", font=("Cairo", 12), bg="#dff9fb").pack(side="right")
    search_entry = tk.Entry(search_frame, textvariable=search_var, font=("Cairo", 12), width=30)
    search_entry.pack(side="right", padx=5)
    tk.Button(search_frame, text="بحث", command=search_products, bg="#487eb0", fg="white", font=("Cairo", 11)).pack(side="right", padx=5)
    search_var.trace_add('write', search_products)
    # جدول المنتجات
    tree_frame = tk.Frame(frame, bg="#dff9fb")
    tree_frame.pack(pady=20, fill="both", expand=1)
    tree_scroll_y = tk.Scrollbar(tree_frame, orient="vertical")
    tree_scroll_y.pack(side="left", fill="y")
    tree_scroll_x = tk.Scrollbar(tree_frame, orient="horizontal")
    tree_scroll_x.pack(side="bottom", fill="x")
    columns = ("#", "اسم المنتج", "سعر الشراء", "سعر البيع", "صافي الربح", "الباركود", "الكمية")
    tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=10,
                        yscrollcommand=tree_scroll_y.set, xscrollcommand=tree_scroll_x.set)
    for col in columns:
        tree.heading(col, text=col)
        tree.column(col, anchor="center", width=120)
    tree.pack(fill="both", expand=1)
    tree_scroll_y.config(command=tree.yview)
    tree_scroll_x.config(command=tree.xview)
    # زر الانتقال لأسفل الجدول
    def scroll_to_bottom_tree():
        children = tree.get_children()
        if children:
            tree.see(children[-1])
    btn_scroll_down = tk.Button(frame, text="الانتقال لأسفل الجدول", command=scroll_to_bottom_tree, bg="#273c75", fg="white", font=("Cairo", 11))
    btn_scroll_down.pack(pady=5, side="bottom")
    # دوال العمليات
    def refresh_products():
        # تحديث قائمة المنتجات من الجدول فقط عند الإضافة/التعديل/الحذف وليس بعد البيع
        products.clear()
        for item in tree.get_children():
            values = tree.item(item)['values']
            if len(values) == 7:
                products.append({
                    'name': str(values[1]),
                    'buy': float(values[2]),
                    'sell': float(values[3]),
                    'profit': str(values[4]),
                    'barcode': str(values[5]),
                    'qty': int(values[6])
                })
        save_data()
        search_products()
    def add_product():
        def calc_profit(*args):
            try:
                buy = float(entry_buy.get())
                sell = float(entry_sell.get())
                profit = sell - buy
                entry_profit.config(state="normal")
                entry_profit.delete(0, tk.END)
                entry_profit.insert(0, str(round(profit, 2)))
                entry_profit.config(state="readonly")
            except:
                entry_profit.config(state="normal")
                entry_profit.delete(0, tk.END)
                entry_profit.insert(0, "")
                entry_profit.config(state="readonly")
        def save():
            name = entry_name.get()
            buy = entry_buy.get()
            sell = entry_sell.get()
            profit = entry_profit.get()
            barcode = entry_barcode.get()
            qty = entry_qty.get()
            if name and buy and sell and profit and barcode and qty:
                products.append({
                    'name': name,
                    'buy': float(buy),
                    'sell': float(sell),
                    'profit': profit,
                    'barcode': barcode,
                    'qty': int(qty)
                })
                save_data()
                load_products_to_tree()
                if 'load_pos_products' in globals():
                    load_pos_products()
                if 'update_dashboard' in globals():
                    update_dashboard()
                add_win.destroy()
                log_action("إضافة منتج", f"{name} - سعر الشراء: {buy} - سعر البيع: {sell} - الكمية: {qty}")
        add_win = tk.Toplevel(root)
        add_win.title("إضافة منتج")
        add_win.geometry("350x400")
        tk.Label(add_win, text="اسم المنتج:").pack(pady=3)
        entry_name = tk.Entry(add_win)
        entry_name.pack()
        tk.Label(add_win, text="سعر الشراء:").pack(pady=3)
        entry_buy = tk.Entry(add_win)
        entry_buy.pack()
        tk.Label(add_win, text="سعر البيع:").pack(pady=3)
        entry_sell = tk.Entry(add_win)
        entry_sell.pack()
        tk.Label(add_win, text="صافي الربح:").pack(pady=3)
        entry_profit = tk.Entry(add_win, state="readonly")
        entry_profit.pack()
        tk.Label(add_win, text="الباركود:").pack(pady=3)
        entry_barcode = tk.Entry(add_win)
        entry_barcode.pack()
        tk.Label(add_win, text="الكمية:").pack(pady=3)
        entry_qty = tk.Entry(add_win)
        entry_qty.pack()
        entry_buy.bind('<KeyRelease>', calc_profit)
        entry_sell.bind('<KeyRelease>', calc_profit)
        tk.Button(add_win, text="حفظ", command=save, bg="#44bd32", fg="white").pack(pady=10)
    def edit_product():
        selected = tree.selection()
        if not selected:
            return
        item = tree.item(selected)
        values = item['values']
        def calc_profit(*args):
            try:
                buy = float(entry_buy.get())
                sell = float(entry_sell.get())
                profit = sell - buy
                entry_profit.config(state="normal")
                entry_profit.delete(0, tk.END)
                entry_profit.insert(0, str(round(profit, 2)))
                entry_profit.config(state="readonly")
            except:
                entry_profit.config(state="normal")
                entry_profit.delete(0, tk.END)
                entry_profit.insert(0, "")
                entry_profit.config(state="readonly")
        def save():
            name = entry_name.get()
            buy = entry_buy.get()
            sell = entry_sell.get()
            profit = entry_profit.get()
            barcode = entry_barcode.get()
            qty = entry_qty.get()
            if name and buy and sell and profit and barcode and qty:
                idx = int(values[0]) - 1
                if 0 <= idx < len(products):
                    products[idx] = {
                        'name': name,
                        'buy': float(buy),
                        'sell': float(sell),
                        'profit': profit,
                        'barcode': barcode,
                        'qty': int(qty)
                    }
                    save_data()
                    load_products_to_tree()
                    if 'load_pos_products' in globals():
                        load_pos_products()
                    if 'update_dashboard' in globals():
                        update_dashboard()
                    edit_win.destroy()
                    log_action("تعديل منتج", f"{name} - سعر الشراء: {buy} - سعر البيع: {sell} - الكمية: {qty}")
    def delete_product():
        selected = tree.selection()
        if selected:
            idx = int(tree.item(selected)['values'][0]) - 1
            if 0 <= idx < len(products):
                del products[idx]
                save_data()
                load_products_to_tree()
                if 'load_pos_products' in globals():
                    load_pos_products()
                if 'update_dashboard' in globals():
                    update_dashboard()
                log_action("حذف منتج", f"تم حذف المنتج: {selected}")
    # أزرار العمليات
    btn_frame = tk.Frame(frame, bg="#dff9fb")
    btn_frame.pack(pady=10)
    tk.Button(btn_frame, text="إضافة", command=add_product, bg="#44bd32", fg="white", width=10).pack(side="right", padx=5)
    tk.Button(btn_frame, text="تعديل", command=edit_product, bg="#e1b12c", fg="black", width=10).pack(side="right", padx=5)
    tk.Button(btn_frame, text="حذف", command=delete_product, bg="#c23616", fg="white", width=10).pack(side="right", padx=5)
    # تحميل المنتجات في الجدول عند بدء التشغيل
    def load_products_to_tree():
        tree.delete(*tree.get_children())
        for idx, p in enumerate(products, 1):
            tree.insert("", "end", values=(idx, p['name'], p['buy'], p['sell'], p['profit'], p['barcode'], p['qty']))
    load_products_to_tree()

    # --- لوحة تحكم وإحصائيات ---
    dashboard_frame = tk.Frame(frames[0], bg="#dff9fb")
    dashboard_frame.pack(pady=5, fill="x")
    # إحصائيات سريعة
    stat_font = ("Cairo", 13, "bold")
    def update_dashboard():
        # إجمالي المنتجات
        total_products = len(products)
        # إجمالي الكمية
        total_qty = sum(int(p.get('qty', 0)) for p in products)
        # إجمالي العملاء
        total_customers = len(customers) if 'customers' in globals() else 0
        # إجمالي المبيعات
        total_sales = sum(float(s.get('sell', 0)) * int(s.get('qty', 1)) for s in sales_history)
        # إجمالي الربح
        total_profit = sum(float(s.get('profit', 0)) for s in sales_history)
        # المنتجات الأقل كمية
        low_stock = [p['name'] for p in products if int(p.get('qty', 0)) <= LOW_STOCK_LIMIT]
        # تحديث القيم
        lbl_products.config(text=f"عدد المنتجات: {total_products}")
        lbl_qty.config(text=f"إجمالي الكمية: {total_qty}")
        lbl_customers.config(text=f"عدد العملاء: {total_customers}")
        lbl_sales.config(text=f"إجمالي المبيعات: {total_sales:.2f}")
        lbl_profit.config(text=f"إجمالي الربح: {total_profit:.2f}")
        lbl_lowstock.config(text=f"منتجات منخفضة: {', '.join(low_stock) if low_stock else 'لا يوجد'}")
    lbl_products = tk.Label(dashboard_frame, font=stat_font, bg="#dff9fb", fg="#273c75")
    lbl_products.pack(side="right", padx=10)
    lbl_qty = tk.Label(dashboard_frame, font=stat_font, bg="#dff9fb", fg="#273c75")
    lbl_qty.pack(side="right", padx=10)
    lbl_customers = tk.Label(dashboard_frame, font=stat_font, bg="#dff9fb", fg="#273c75")
    lbl_customers.pack(side="right", padx=10)
    lbl_sales = tk.Label(dashboard_frame, font=stat_font, bg="#dff9fb", fg="#44bd32")
    lbl_sales.pack(side="right", padx=10)
    lbl_profit = tk.Label(dashboard_frame, font=stat_font, bg="#dff9fb", fg="#e1b12c")
    lbl_profit.pack(side="right", padx=10)
    lbl_lowstock = tk.Label(dashboard_frame, font=stat_font, bg="#dff9fb", fg="#c23616")
    lbl_lowstock.pack(side="right", padx=10)
    # تحديث الإحصائيات عند تحميل البيانات أو البيع أو الحفظ
    update_dashboard()

# --- تبويب العملاء ---
if tab_names[1] == "العملاء":
    frame = frames[1]
    # عنوان التبويب
    customer_title = tk.Label(frame, text="إدارة العملاء", font=("Cairo", 22, "bold"), bg="#dff9fb", fg="#273c75")
    customer_title.pack(pady=10)
    # حقل البحث
    customer_search_var = tk.StringVar()
    def search_customers(*args):
        query = customer_search_var.get().strip().lower()
        customer_tree.delete(*customer_tree.get_children())
        for idx, c in enumerate(customers, 1):
            if query in str(c['name']).lower() or query in str(c['phone']).lower() or query in str(c['address']).lower():
                customer_tree.insert("", "end", values=(idx, c['name'], c['phone'], c['address'], c['balance']))
    search_frame = tk.Frame(frame, bg="#dff9fb")
    search_frame.pack(pady=(10,0))
    tk.Label(search_frame, text="بحث:", font=("Cairo", 12), bg="#dff9fb").pack(side="right")
    search_entry = tk.Entry(search_frame, textvariable=customer_search_var, font=("Cairo", 12), width=30)
    search_entry.pack(side="right", padx=5)
    tk.Button(search_frame, text="بحث", command=search_customers, bg="#487eb0", fg="white", font=("Cairo", 11)).pack(side="right", padx=5)
    customer_search_var.trace_add('write', search_customers)
    # جدول العملاء
    customer_tree_frame = tk.Frame(frame, bg="#dff9fb")
    customer_tree_frame.pack(pady=20, fill="both", expand=1)
    customer_tree_scroll_y = tk.Scrollbar(customer_tree_frame, orient="vertical")
    customer_tree_scroll_y.pack(side="left", fill="y")
    customer_tree_scroll_x = tk.Scrollbar(customer_tree_frame, orient="horizontal")
    customer_tree_scroll_x.pack(side="bottom", fill="x")
    customer_columns = ("#", "اسم العميل", "الهاتف", "العنوان", "الرصيد")
    customer_tree = ttk.Treeview(customer_tree_frame, columns=customer_columns, show="headings", height=10,
                        yscrollcommand=customer_tree_scroll_y.set, xscrollcommand=customer_tree_scroll_x.set)
    for col in customer_columns:
        customer_tree.heading(col, text=col)
        customer_tree.column(col, anchor="center", width=140)
    customer_tree.pack(fill="both", expand=1)
    customer_tree_scroll_y.config(command=customer_tree.yview)
    customer_tree_scroll_x.config(command=customer_tree.xview)
    # دوال العمليات
    def refresh_customers():
        customers.clear()
        for item in customer_tree.get_children():
            values = customer_tree.item(item)['values']
            if len(values) == 5:
                customers.append({
                    'name': str(values[1]),
                    'phone': str(values[2]),
                    'address': str(values[3]),
                    'balance': float(values[4])
                })
        save_data()
        search_customers()
    def add_customer():
        def save():
            name = entry_name.get()
            phone = entry_phone.get()
            address = entry_address.get()
            balance = entry_balance.get()
            if name and phone and address and balance:
                customer_tree.insert("", "end", values=(len(customer_tree.get_children())+1, name, phone, address, balance))
                add_win.destroy()
                refresh_customers()
                log_action("إضافة عميل", f"{name} - الهاتف: {phone} - العنوان: {address}")
        add_win = tk.Toplevel(root)
        add_win.title("إضافة عميل")
        add_win.geometry("350x350")
        tk.Label(add_win, text="اسم العميل:").pack(pady=3)
        entry_name = tk.Entry(add_win)
        entry_name.pack()
        tk.Label(add_win, text="الهاتف:").pack(pady=3)
        entry_phone = tk.Entry(add_win)
        entry_phone.pack()
        tk.Label(add_win, text="العنوان:").pack(pady=3)
        entry_address = tk.Entry(add_win)
        entry_address.pack()
        tk.Label(add_win, text="الرصيد:").pack(pady=3)
        entry_balance = tk.Entry(add_win)
        entry_balance.insert(0, "0")
        entry_balance.pack()
        tk.Button(add_win, text="حفظ", command=save, bg="#44bd32", fg="white").pack(pady=10)
    def edit_customer():
        selected = customer_tree.selection()
        if not selected:
            return
        item = customer_tree.item(selected)
        values = item['values']
        def save():
            name = entry_name.get()
            phone = entry_phone.get()
            address = entry_address.get()
            balance = entry_balance.get()
            if name and phone and address and balance:
                customer_tree.item(selected, values=(values[0], name, phone, address, balance))
                edit_win.destroy()
                refresh_customers()
                log_action("تعديل عميل", f"{name} - الهاتف: {phone} - العنوان: {address}")
        edit_win = tk.Toplevel(root)
        edit_win.title("تعديل عميل")
        edit_win.geometry("350x350")
        tk.Label(edit_win, text="اسم العميل:").pack(pady=3)
        entry_name = tk.Entry(edit_win)
        entry_name.insert(0, values[1])
        entry_name.pack()
        tk.Label(edit_win, text="الهاتف:").pack(pady=3)
        entry_phone = tk.Entry(edit_win)
        entry_phone.insert(0, values[2])
        entry_phone.pack()
        tk.Label(edit_win, text="العنوان:").pack(pady=3)
        entry_address = tk.Entry(edit_win)
        entry_address.insert(0, values[3])
        entry_address.pack()
        tk.Label(edit_win, text="الرصيد:").pack(pady=3)
        entry_balance = tk.Entry(edit_win)
        entry_balance.insert(0, values[4])
        entry_balance.pack()
        tk.Button(edit_win, text="حفظ", command=save, bg="#e1b12c", fg="black").pack(pady=10)
    def delete_customer():
        selected = customer_tree.selection()
        if selected:
            customer_tree.delete(selected)
            refresh_customers()
            log_action("حذف عميل", f"تم حذف العميل: {selected}")
    # أزرار العمليات
    btn_frame = tk.Frame(frame, bg="#dff9fb")
    btn_frame.pack(pady=10)
    tk.Button(btn_frame, text="إضافة", command=add_customer, bg="#44bd32", fg="white", width=10).pack(side="right", padx=5)
    tk.Button(btn_frame, text="تعديل", command=edit_customer, bg="#e1b12c", fg="black", width=10).pack(side="right", padx=5)
    tk.Button(btn_frame, text="حذف", command=delete_customer, bg="#c23616", fg="white", width=10).pack(side="right", padx=5)
    # تحميل العملاء في الجدول عند بدء التشغيل
    def load_customers_to_tree():
        customer_tree.delete(*customer_tree.get_children())
        for idx, c in enumerate(customers, 1):
            customer_tree.insert("", "end", values=(idx, c['name'], c['phone'], c['address'], c['balance']))
    # تعريف قائمة العملاء في البيانات
    if 'customers' not in globals():
        customers = []
    load_customers_to_tree()

# --- تبويب نقطة البيع (بيع المنتج وتحديث الكمية في المخزون) ---
if tab_names[3] == "نقطة البيع":
    frame = frames[3]
    pos_title = tk.Label(frame, text="نقطة البيع الذكية", font=("Cairo", 22, "bold"), bg="#dff9fb", fg="#273c75")
    pos_title.pack(pady=10)
    # حقل البحث
    pos_search_var = tk.StringVar()
    def search_pos_products(*args):
        query = pos_search_var.get().strip().lower()
        pos_tree.delete(*pos_tree.get_children())
        for idx, p in enumerate(products, 1):
            if query in str(p['name']).lower() or query in str(p['barcode']).lower():
                pos_tree.insert("", "end", values=(idx, p['name'], p['barcode'], p['qty'], p['sell']))
    search_frame = tk.Frame(frame, bg="#dff9fb")
    search_frame.pack(pady=(10,0))
    tk.Label(search_frame, text="بحث المنتج:", font=("Cairo", 12), bg="#dff9fb").pack(side="right")
    search_entry = tk.Entry(search_frame, textvariable=pos_search_var, font=("Cairo", 12), width=30)
    search_entry.pack(side="right", padx=5)
    tk.Button(search_frame, text="بحث", command=search_pos_products, bg="#487eb0", fg="white", font=("Cairo", 11)).pack(side="right", padx=5)
    pos_search_var.trace_add('write', search_pos_products)
    # جدول المنتجات
    pos_tree_frame = tk.Frame(frame, bg="#dff9fb")
    pos_tree_frame.pack(pady=20, fill="both", expand=1)
    pos_tree_scroll_y = tk.Scrollbar(pos_tree_frame, orient="vertical")
    pos_tree_scroll_y.pack(side="left", fill="y")
    pos_tree_scroll_x = tk.Scrollbar(pos_tree_frame, orient="horizontal")
    pos_tree_scroll_x.pack(side="bottom", fill="x")
    pos_columns = ("#", "اسم المنتج", "الباركود", "الكمية", "سعر البيع")
    pos_tree = ttk.Treeview(pos_tree_frame, columns=pos_columns, show="headings", height=10,
                        yscrollcommand=pos_tree_scroll_y.set, xscrollcommand=pos_tree_scroll_x.set)
    for col in pos_columns:
        pos_tree.heading(col, text=col)
        pos_tree.column(col, anchor="center", width=120)
    pos_tree.pack(fill="both", expand=1)
    pos_tree_scroll_y.config(command=pos_tree.yview)
    pos_tree_scroll_x.config(command=pos_tree.xview)
    # تحميل كل المنتجات عند بدء التشغيل
    def load_pos_products():
        pos_tree.delete(*pos_tree.get_children())
        for idx, p in enumerate(products, 1):
            pos_tree.insert("", "end", values=(idx, p['name'], p['barcode'], p['qty'], p['sell']))
    load_pos_products()
    # دالة البيع من الجدول
    def sell_selected_product():
        selected = pos_tree.selection()
        if not selected:
            messagebox.showwarning("تنبيه", "يرجى اختيار منتج للبيع")
            return
        item = pos_tree.item(selected)
        values = item['values']
        product_name = values[1]
        barcode = values[2]
        available_qty = int(values[3])
        price = float(values[4])
        # نافذة تحديد الكمية
        def do_sell():
            try:
                qty = int(qty_entry.get())
                if qty <= 0 or qty > available_qty:
                    raise ValueError
            except:
                messagebox.showerror("خطأ", "الكمية غير صحيحة أو غير متوفرة")
                return
            for p in products:
                if p['name'] == product_name and str(p['barcode']) == str(barcode):
                    p['qty'] -= qty
                    profit = float(p['sell']) - float(p['buy'])
                    now = datetime.datetime.now()
                    sales_history.append({
                        'name': p['name'],
                        'barcode': p['barcode'],
                        'sell': float(p['sell']),
                        'buy': float(p['buy']),
                        'qty': qty,
                        'profit': profit * qty,
                        'date': now
                    })
                    save_data()
                    load_products_to_tree()
                    load_pos_products()
                    if 'update_dashboard' in globals():
                        update_dashboard()
                    if 'update_profit_stats' in globals():
                        update_profit_stats()
                    messagebox.showinfo("تم البيع", f"تم بيع {qty} من {p['name']}.")
                    log_action("بيع منتج", f"{p['name']} - الكمية: {qty}")
                    break
            sell_win.destroy()
        sell_win = tk.Toplevel(root)
        sell_win.title("بيع المنتج")
        sell_win.geometry("350x220")
        sell_win.resizable(False, False)
        tk.Label(sell_win, text=f"اسم المنتج: {product_name}", font=("Cairo", 14, "bold"), fg="#273c75").pack(pady=7)
        tk.Label(sell_win, text=f"الكمية المتوفرة: {available_qty}", font=("Cairo", 13)).pack(pady=3)
        tk.Label(sell_win, text="الكمية المراد بيعها:", font=("Cairo", 13)).pack(pady=3)
        qty_entry = tk.Entry(sell_win, font=("Cairo", 14), justify="center")
        qty_entry.insert(0, "1")
        qty_entry.pack(pady=5)
        sell_btn = tk.Button(sell_win, text="بيع الآن", command=do_sell, bg="#e1b12c", fg="#2d3436", font=("Cairo", 15, "bold"), width=15, height=2)
        sell_btn.pack(pady=12)
        qty_entry.focus()
    # زر بيع احترافي أسفل الجدول
    btn_sell = tk.Button(frame, text="🛒 بيع المنتج المحدد", command=sell_selected_product, bg="#e1b12c", fg="#2d3436", font=("Cairo", 18, "bold"), width=22, height=2, cursor="hand2")
    btn_sell.pack(pady=18)

# --- تبويب التقارير ---
if tab_names[4] == "التقارير":
    frame = frames[4]
    report_title = tk.Label(frame, text="تقارير المبيعات", font=("Cairo", 22, "bold"), bg="#dff9fb", fg="#273c75")
    report_title.pack(pady=10)

    # --- تقارير الربح الدورية ---
    def calc_profit_stats():
        today = datetime.datetime.now().date()
        profit_day = 0
        profit_week = 0
        profit_month = 0
        profit_quarter = 0
        profit_year = 0
        for s in sales_history:
            s_date = s['date']
            if isinstance(s_date, str):
                try:
                    s_date = datetime.datetime.strptime(s_date, "%Y-%m-%d %H:%M:%S")
                except:
                    continue
            s_date = s_date.date()
            profit = float(s.get('profit', 0))
            # يومي
            if s_date == today:
                profit_day += profit
            # أسبوعي
            if s_date >= today - datetime.timedelta(days=6):
                profit_week += profit
            # شهري
            if s_date.year == today.year and s_date.month == today.month:
                profit_month += profit
            # ربع سنوي (كل 3 أشهر)
            quarter = (today.month-1)//3 + 1
            s_quarter = (s_date.month-1)//3 + 1
            if s_date.year == today.year and s_quarter == quarter:
                profit_quarter += profit
            # سنوي
            if s_date.year == today.year:
                profit_year += profit
        return profit_day, profit_week, profit_month, profit_quarter, profit_year

    def update_profit_stats():
        p_day, p_week, p_month, p_quarter, p_year = calc_profit_stats()
        stats_text = f"ربح اليوم: {p_day:.2f} | الأسبوع: {p_week:.2f} | الشهر: {p_month:.2f} | 3 أشهر: {p_quarter:.2f} | السنة: {p_year:.2f}"
        if hasattr(frame, 'profit_stats_label'):
            frame.profit_stats_label.config(text=stats_text)
        else:
            frame.profit_stats_label = tk.Label(frame, text=stats_text, font=("Cairo", 15, "bold"), bg="#dff9fb", fg="#44bd32")
            frame.profit_stats_label.pack(pady=5)

    # تحديث الإحصائيات عند كل تحميل أو بيع
    def reload_and_update_stats():
        load_sales_to_tree()
        update_profit_stats()
    # خيارات التقرير
    report_frame = tk.Frame(frame, bg="#dff9fb")
    report_frame.pack(pady=10)
    tk.Label(report_frame, text="من تاريخ:", font=("Cairo", 12), bg="#dff9fb").pack(side="right", padx=2)
    from_date = tk.Entry(report_frame, font=("Cairo", 12), width=12)
    from_date.pack(side="right", padx=2)
    tk.Label(report_frame, text="إلى تاريخ:", font=("Cairo", 12), bg="#dff9fb").pack(side="right", padx=2)
    to_date = tk.Entry(report_frame, font=("Cairo", 12), width=12)
    to_date.pack(side="right", padx=2)
    def filter_sales():
        sales_tree.delete(*sales_tree.get_children())
        f_date = from_date.get().strip()
        t_date = to_date.get().strip()
        for idx, s in enumerate(sales_history, 1):
            date_str = s['date'].strftime("%Y-%m-%d %H:%M:%S") if isinstance(s['date'], datetime.datetime) else str(s['date'])
            if (not f_date or date_str >= f_date) and (not t_date or date_str <= t_date):
                sales_tree.insert("", "end", values=(idx, s['name'], s['barcode'], s['qty'], s['sell'], s['buy'], s['profit'], date_str))
        update_profit_stats()
    tk.Button(report_frame, text="عرض التقرير", command=filter_sales, bg="#487eb0", fg="white", font=("Cairo", 11)).pack(side="right", padx=10)
    # جدول المبيعات
    sales_tree_frame = tk.Frame(frame, bg="#dff9fb")
    sales_tree_frame.pack(pady=20, fill="both", expand=1)
    sales_tree_scroll_y = tk.Scrollbar(sales_tree_frame, orient="vertical")
    sales_tree_scroll_y.pack(side="left", fill="y")
    sales_tree_scroll_x = tk.Scrollbar(sales_tree_frame, orient="horizontal")
    sales_tree_scroll_x.pack(side="bottom", fill="x")
    sales_columns = ("#", "المنتج", "الباركود", "الكمية", "سعر البيع", "سعر الشراء", "الربح", "التاريخ")
    sales_tree = ttk.Treeview(sales_tree_frame, columns=sales_columns, show="headings", height=10,
                        yscrollcommand=sales_tree_scroll_y.set, xscrollcommand=sales_tree_scroll_x.set)
    for col in sales_columns:
        sales_tree.heading(col, text=col)
        sales_tree.column(col, anchor="center", width=120)
    sales_tree.pack(fill="both", expand=1)
    sales_tree_scroll_y.config(command=sales_tree.yview)
    sales_tree_scroll_x.config(command=sales_tree.xview)
    # تحميل كل المبيعات عند بدء التشغيل
    def load_sales_to_tree():
        sales_tree.delete(*sales_tree.get_children())
        for idx, s in enumerate(sales_history, 1):
            date_str = s['date'].strftime("%Y-%m-%d %H:%M:%S") if isinstance(s['date'], datetime.datetime) else str(s['date'])
            sales_tree.insert("", "end", values=(idx, s['name'], s['barcode'], s['qty'], s['sell'], s['buy'], s['profit'], date_str))
    load_sales_to_tree()
    update_profit_stats()
    # زر إلغاء المباع
    def cancel_sale():
        selected = sales_tree.selection()
        if not selected:
            messagebox.showwarning("تنبيه", "يرجى اختيار عملية بيع لإلغائها")
            return
        item = sales_tree.item(selected)
        values = item['values']
        idx = int(values[0]) - 1
        if idx < 0 or idx >= len(sales_history):
            messagebox.showerror("خطأ", "تعذر العثور على عملية البيع المحددة")
            return
        sale = sales_history[idx]
        # رسالة تأكيد
        confirm = messagebox.askyesno("تأكيد الإلغاء", f"هل أنت متأكد من إلغاء بيع المنتج '{sale['name']}' بكمية {sale['qty']}؟ سيتم إرجاع الكمية للمخزون وحذف السطر من سجل المبيعات.")
        if not confirm:
            return
        # إرجاع الكمية للمخزون
        for p in products:
            if p['name'] == sale['name'] and str(p['barcode']) == str(sale['barcode']):
                p['qty'] += int(sale['qty'])
                break
        # حذف السطر من سجل المبيعات
        del sales_history[idx]
        save_data()
        load_sales_to_tree()
        update_profit_stats()
        # تسجيل العملية في سجل العمليات
        if 'log_action' in globals():
            log_action("إلغاء بيع", f"تم إلغاء بيع المنتج '{sale['name']}' بكمية {sale['qty']} وإرجاعها للمخزون.")
        messagebox.showinfo("تم الإلغاء", "تم إلغاء عملية البيع وإرجاع الكمية للمخزون.")
    btn_cancel_sale = tk.Button(frame, text="إلغاء المباع", command=cancel_sale, bg="#c23616", fg="white", font=("Cairo", 13), width=15)
    btn_cancel_sale.pack(pady=5)

# --- تبويب مشتركين الانترنت ---
if tab_names[5] == "مشتركين الانترنت":
    frame = frames[5]
    sub_title = tk.Label(frame, text="إدارة مشتركين الانترنت", font=("Cairo", 22, "bold"), bg="#dff9fb", fg="#273c75")
    sub_title.pack(pady=10)
    # حقل البحث
    sub_search_var = tk.StringVar()
    def search_subscribers(*args):
        query = sub_search_var.get().strip().lower()
        sub_tree.delete(*sub_tree.get_children())
        for idx, s in enumerate(subscribers, 1):
            if query in str(s['name']).lower() or query in str(s['phone']).lower() or query in str(s['address']).lower():
                sub_tree.insert("", "end", values=(idx, s['name'], s['phone'], s['address'], s['price'], s['paid'], s['status'], s['expiry']))
    search_frame = tk.Frame(frame, bg="#dff9fb")
    search_frame.pack(pady=(10,0))
    tk.Label(search_frame, text="بحث:", font=("Cairo", 12), bg="#dff9fb").pack(side="right")
    search_entry = tk.Entry(search_frame, textvariable=sub_search_var, font=("Cairo", 12), width=30)
    search_entry.pack(side="right", padx=5)
    tk.Button(search_frame, text="بحث", command=search_subscribers, bg="#487eb0", fg="white", font=("Cairo", 11)).pack(side="right", padx=5)
    sub_search_var.trace_add('write', search_subscribers)
    # جدول المشتركين
    sub_tree_frame = tk.Frame(frame, bg="#dff9fb")
    sub_tree_frame.pack(pady=20, fill="both", expand=1)
    sub_tree_scroll_y = tk.Scrollbar(sub_tree_frame, orient="vertical")
    sub_tree_scroll_y.pack(side="left", fill="y")
    sub_tree_scroll_x = tk.Scrollbar(sub_tree_frame, orient="horizontal")
    sub_tree_scroll_x.pack(side="bottom", fill="x")
    sub_columns = ("#", "الاسم", "الهاتف", "العنوان", "سعر الاشتراك", "المبلغ المسدد", "الحالة", "تاريخ الانتهاء")
    sub_tree = ttk.Treeview(sub_tree_frame, columns=sub_columns, show="headings", height=10,
                        yscrollcommand=sub_tree_scroll_y.set, xscrollcommand=sub_tree_scroll_x.set)
    for col in sub_columns:
        sub_tree.heading(col, text=col)
        sub_tree.column(col, anchor="center", width=120)
    sub_tree.pack(fill="both", expand=1)
    sub_tree_scroll_y.config(command=sub_tree.yview)
    sub_tree_scroll_x.config(command=sub_tree.xview)
    # دوال العمليات
    def refresh_subscribers():
        subscribers.clear()
        for item in sub_tree.get_children():
            values = sub_tree.item(item)['values']
            if len(values) == 8:
                subscribers.append({
                    'name': str(values[1]),
                    'phone': str(values[2]),
                    'address': str(values[3]),
                    'price': float(values[4]),
                    'paid': float(values[5]),
                    'status': str(values[6]),
                    'expiry': str(values[7])
                })
        save_data()
        search_subscribers()
        update_subscriber_stats()
    def add_subscriber():
        def save():
            name = entry_name.get()
            phone = entry_phone.get()
            address = entry_address.get()
            price = entry_price.get()
            paid = entry_paid.get()
            status = status_var.get()
            expiry = entry_expiry.get()
            if name and phone and address and price and paid and status and expiry:
                sub_tree.insert("", "end", values=(len(sub_tree.get_children())+1, name, phone, address, price, paid, status, expiry))
                add_win.destroy()
                refresh_subscribers()
                log_action("إضافة مشترك", f"{name} - الهاتف: {phone} - العنوان: {address} - سعر الاشتراك: {price} - المسدد: {paid} - الحالة: {status}")
        add_win = tk.Toplevel(root)
        add_win.title("إضافة مشترك")
        add_win.geometry("350x400")
        tk.Label(add_win, text="الاسم:").pack(pady=3)
        entry_name = tk.Entry(add_win)
        entry_name.pack()
        tk.Label(add_win, text="الهاتف:").pack(pady=3)
        entry_phone = tk.Entry(add_win)
        entry_phone.pack()
        tk.Label(add_win, text="العنوان:").pack(pady=3)
        entry_address = tk.Entry(add_win)
        entry_address.pack()
        tk.Label(add_win, text="سعر الاشتراك:").pack(pady=3)
        entry_price = tk.Entry(add_win)
        entry_price.pack()
        tk.Label(add_win, text="المبلغ المسدد:").pack(pady=3)
        entry_paid = tk.Entry(add_win)
        entry_paid.insert(0, "0")
        entry_paid.pack()
        tk.Label(add_win, text="الحالة:").pack(pady=3)
        status_var = tk.StringVar(value="أجل")
        status_menu = ttk.Combobox(add_win, textvariable=status_var, values=["تم التسديد", "أجل"])
        status_menu.pack()
        tk.Label(add_win, text="تاريخ الانتهاء:").pack(pady=3)
        entry_expiry = tk.Entry(add_win)
        entry_expiry.pack()
        tk.Button(add_win, text="حفظ", command=save, bg="#44bd32", fg="white").pack(pady=10)
    def edit_subscriber():
        selected = sub_tree.selection()
        if not selected:
            return
        item = sub_tree.item(selected)
        values = item['values']
        def save():
            name = entry_name.get()
            phone = entry_phone.get()
            address = entry_address.get()
            price = entry_price.get()
            paid = entry_paid.get()
            status = status_var.get()
            expiry = entry_expiry.get()
            if name and phone and address and price and paid and status and expiry:
                sub_tree.item(selected, values=(values[0], name, phone, address, price, paid, status, expiry))
                edit_win.destroy()
                refresh_subscribers()
                log_action("تعديل مشترك", f"{name} - الهاتف: {phone} - العنوان: {address} - سعر الاشتراك: {price} - المسدد: {paid} - الحالة: {status}")
        edit_win = tk.Toplevel(root)
        edit_win.title("تعديل مشترك")
        edit_win.geometry("350x400")
        tk.Label(edit_win, text="الاسم:").pack(pady=3)
        entry_name = tk.Entry(edit_win)
        entry_name.insert(0, values[1])
        entry_name.pack()
        tk.Label(edit_win, text="الهاتف:").pack(pady=3)
        entry_phone = tk.Entry(edit_win)
        entry_phone.insert(0, values[2])
        entry_phone.pack()
        tk.Label(edit_win, text="العنوان:").pack(pady=3)
        entry_address = tk.Entry(edit_win)
        entry_address.insert(0, values[3])
        entry_address.pack()
        tk.Label(edit_win, text="سعر الاشتراك:").pack(pady=3)
        entry_price = tk.Entry(edit_win)
        entry_price.insert(0, values[4])
        entry_price.pack()
        tk.Label(edit_win, text="المبلغ المسدد:").pack(pady=3)
        entry_paid = tk.Entry(edit_win)
        entry_paid.insert(0, values[5])
        entry_paid.pack()
        tk.Label(edit_win, text="الحالة:").pack(pady=3)
        status_var = tk.StringVar(value=values[6])
        status_menu = ttk.Combobox(edit_win, textvariable=status_var, values=["تم التسديد", "أجل"])
        status_menu.pack()
        tk.Label(edit_win, text="تاريخ الانتهاء:").pack(pady=3)
        entry_expiry = tk.Entry(edit_win)
        entry_expiry.insert(0, values[7])
        entry_expiry.pack()
        tk.Button(edit_win, text="حفظ", command=save, bg="#e1b12c", fg="black").pack(pady=10)
    def delete_subscriber():
        selected = sub_tree.selection()
        if selected:
            sub_tree.delete(selected)
            refresh_subscribers()
            log_action("حذف مشترك", f"تم حذف المشترك: {selected}")
    def toggle_status():
        selected = sub_tree.selection()
        if selected:
            item = sub_tree.item(selected)
            values = list(item['values'])
            values[6] = "تم التسديد" if values[6] == "أجل" else "أجل"
            sub_tree.item(selected, values=values)
            refresh_subscribers()
    # أزرار العمليات
    btn_frame = tk.Frame(frame, bg="#dff9fb")
    btn_frame.pack(pady=10)
    tk.Button(btn_frame, text="إضافة", command=add_subscriber, bg="#44bd32", fg="white", width=10).pack(side="right", padx=5)
    tk.Button(btn_frame, text="تعديل", command=edit_subscriber, bg="#e1b12c", fg="black", width=10).pack(side="right", padx=5)
    tk.Button(btn_frame, text="حذف", command=delete_subscriber, bg="#c23616", fg="white", width=10).pack(side="right", padx=5)
    tk.Button(btn_frame, text="تغيير حالة التسديد", command=toggle_status, bg="#487eb0", fg="white", width=15).pack(side="right", padx=5)
    # تحميل المشتركين في الجدول عند بدء التشغيل
    def load_subscribers_to_tree():
        sub_tree.delete(*sub_tree.get_children())
        for idx, s in enumerate(subscribers, 1):
            sub_tree.insert("", "end", values=(idx, s['name'], s['phone'], s['address'], s.get('price',0), s.get('paid',0), s.get('status','أجل'), s['expiry']))
        update_subscriber_stats()
    def update_subscriber_stats():
        total_paid = sum(float(s.get('paid',0)) for s in subscribers if s.get('status')=="تم التسديد")
        total_unpaid = sum(float(s.get('price',0))-float(s.get('paid',0)) for s in subscribers if s.get('status')!="تم التسديد")
        stats_text = f"إجمالي المسدد: {total_paid} | إجمالي غير المسدد: {total_unpaid}"
        if hasattr(frame, 'stats_label'):
            frame.stats_label.config(text=stats_text)
        else:
            frame.stats_label = tk.Label(frame, text=stats_text, font=("Cairo", 14, "bold"), bg="#dff9fb", fg="#273c75")
            frame.stats_label.pack(pady=5)
    load_subscribers_to_tree()

# --- تبويب الحفظ والنسخ الاحتياطي ---
if tab_names[6] == "الحفظ والنسخ الاحتياطي":
    frame = frames[6]
    backup_title = tk.Label(frame, text="الحفظ والنسخ الاحتياطي", font=("Cairo", 22, "bold"), bg="#dff9fb", fg="#273c75")
    backup_title.pack(pady=10)
    # زر حفظ البيانات
    def save_all_data():
        save_data()
        messagebox.showinfo("تم الحفظ", "تم حفظ جميع البيانات بنجاح!")
    save_btn = tk.Button(frame, text="حفظ البيانات", command=save_all_data, bg="#487eb0", fg="white", font=("Cairo", 14), width=20)
    save_btn.pack(pady=10)
    # زر النسخ الاحتياطي
    def backup_data():
        backup_path = filedialog.asksaveasfilename(defaultextension=".json", filetypes=[("JSON Files", "*.json")], title="حفظ نسخة احتياطية")
        if backup_path:
            try:
                with open(DATA_FILE, "r", encoding="utf-8") as src, open(backup_path, "w", encoding="utf-8") as dst:
                    dst.write(src.read())
                messagebox.showinfo("تم النسخ الاحتياطي", "تم إنشاء نسخة احتياطية بنجاح!")
            except Exception as e:
                messagebox.showerror("خطأ في النسخ الاحتياطي", str(e))
    backup_btn = tk.Button(frame, text="إنشاء نسخة احتياطية", command=backup_data, bg="#e1b12c", fg="black", font=("Cairo", 14), width=20)
    backup_btn.pack(pady=10)
    # زر استرجاع النسخة الاحتياطية
    def restore_data():
        restore_path = filedialog.askopenfilename(filetypes=[("JSON Files", "*.json")], title="استرجاع نسخة احتياطية")
        if restore_path:
            try:
                with open(restore_path, "r", encoding="utf-8") as src, open(DATA_FILE, "w", encoding="utf-8") as dst:
                    dst.write(src.read())
                load_data()
                # تحديث جميع الجداول بعد الاسترجاع
                if 'load_products_to_tree' in globals():
                    load_products_to_tree()
                if 'load_customers_to_tree' in globals():
                    load_customers_to_tree()
                if 'load_sales_to_tree' in globals():
                    load_sales_to_tree()
                if 'load_subscribers_to_tree' in globals():
                    load_subscribers_to_tree()
                if 'load_debts_to_tree' in globals():
                    load_debts_to_tree()
                messagebox.showinfo("تم الاسترجاع", "تم استرجاع البيانات بنجاح!")
            except Exception as e:
                messagebox.showerror("خطأ في الاسترجاع", str(e))
    restore_btn = tk.Button(frame, text="استرجاع نسخة احتياطية", command=restore_data, bg="#44bd32", fg="white", font=("Cairo", 14), width=20)
    restore_btn.pack(pady=10)
    # --- تصدير واستيراد البيانات (Excel) ---
    def export_to_excel():
        try:
            export_path = filedialog.asksaveasfilename(defaultextension=".csv", filetypes=[("CSV Files", "*.csv")], title="تصدير البيانات")
            if not export_path:
                return
            with open(export_path, "w", encoding="utf-8-sig", newline='') as f:
                writer = csv.writer(f)
                writer.writerow(["نوع البيانات", "البيانات"])
                # تصدير المنتجات
                writer.writerow(["--- المنتجات ---", ""])
                writer.writerow(["اسم المنتج", "سعر الشراء", "سعر البيع", "الربح", "الباركود", "الكمية"])
                for p in products:
                    writer.writerow([p['name'], p['buy'], p['sell'], p['profit'], p['barcode'], p['qty']])
                # تصدير العملاء
                writer.writerow(["--- العملاء ---", ""])
                writer.writerow(["اسم العميل", "الهاتف", "العنوان", "الرصيد"])
                for c in customers:
                    writer.writerow([c['name'], c['phone'], c['address'], c['balance']])
                # تصدير المبيعات
                writer.writerow(["--- المبيعات ---", ""])
                writer.writerow(["المنتج", "الباركود", "الكمية", "سعر البيع", "سعر الشراء", "الربح", "التاريخ"])
                for s in sales_history:
                    date_str = s['date'].strftime("%Y-%m-%d %H:%M:%S") if hasattr(s['date'], 'strftime') else str(s['date'])
                    writer.writerow([s['name'], s['barcode'], s['qty'], s['sell'], s['buy'], s['profit'], date_str])
                # تصدير المشتركين
                writer.writerow(["--- المشتركين ---", ""])
                writer.writerow(["الاسم", "الهاتف", "العنوان", "نوع الاشتراك", "تاريخ الانتهاء"])
                for sub in subscribers:
                    writer.writerow([sub['name'], sub['phone'], sub['address'], sub['subscription'], sub['expiry']])
                # تصدير الديون
                writer.writerow(["--- الديون ---", ""])
                writer.writerow(["اسم العميل", "الهاتف", "المبلغ", "ملاحظة"])
                for d in debts:
                    writer.writerow([d['customer'], d['phone'], d['amount'], d['note']])
            messagebox.showinfo("تم التصدير", "تم تصدير جميع البيانات إلى ملف CSV بنجاح!")
        except Exception as e:
            messagebox.showerror("خطأ في التصدير", str(e))

    def import_from_excel():
        try:
            import_path = filedialog.askopenfilename(filetypes=[("CSV Files", "*.csv")], title="استيراد البيانات")
            if not import_path:
                return
            with open(import_path, "r", encoding="utf-8-sig") as f:
                reader = csv.reader(f)
                section = None
                for row in reader:
                    if not row or row[0].startswith('---'):
                        section = row[0] if row else None
                        continue
                    if section == '--- المنتجات ---' and len(row) == 6:
                        products.append({'name': row[0], 'buy': float(row[1]), 'sell': float(row[2]), 'profit': row[3], 'barcode': row[4], 'qty': int(row[5])})
                    elif section == '--- العملاء ---' and len(row) == 4:
                        customers.append({'name': row[0], 'phone': row[1], 'address': row[2], 'balance': float(row[3])})
                    elif section == '--- المبيعات ---' and len(row) == 7:
                        sales_history.append({'name': row[0], 'barcode': row[1], 'qty': int(row[2]), 'sell': float(row[3]), 'buy': float(row[4]), 'profit': float(row[5]), 'date': row[6]})
                    elif section == '--- المشتركين ---' and len(row) == 5:
                        subscribers.append({'name': row[0], 'phone': row[1], 'address': row[2], 'subscription': row[3], 'expiry': row[4]})
                    elif section == '--- الديون ---' and len(row) == 4:
                        debts.append({'customer': row[0], 'phone': row[1], 'amount': float(row[2]), 'note': row[3]})
            save_data()
            if 'load_products_to_tree' in globals():
                load_products_to_tree()
            if 'load_customers_to_tree' in globals():
                load_customers_to_tree()
            if 'load_sales_to_tree' in globals():
                load_sales_to_tree()
            if 'load_subscribers_to_tree' in globals():
                load_subscribers_to_tree()
            if 'load_debts_to_tree' in globals():
                load_debts_to_tree()
            messagebox.showinfo("تم الاستيراد", "تم استيراد البيانات من ملف CSV بنجاح!")
        except Exception as e:
            messagebox.showerror("خطأ في الاستيراد", str(e))
    # أزرار التصدير والاستيراد
    export_btn = tk.Button(frame, text="تصدير البيانات (CSV)", command=export_to_excel, bg="#273c75", fg="white", font=("Cairo", 14), width=20)
    export_btn.pack(pady=10)
    import_btn = tk.Button(frame, text="استيراد البيانات (CSV)", command=import_from_excel, bg="#e84118", fg="white", font=("Cairo", 14), width=20)
    import_btn.pack(pady=10)

# --- تبويب الديون ---
if tab_names[7] == "الديون":
    frame = frames[7]
    debt_title = tk.Label(frame, text="إدارة الديون", font=("Cairo", 22, "bold"), bg="#dff9fb", fg="#273c75")
    debt_title.pack(pady=10)
    # حقل البحث
    debt_search_var = tk.StringVar()
    def search_debts(*args):
        query = debt_search_var.get().strip().lower()
        debt_tree.delete(*debt_tree.get_children())
        for idx, d in enumerate(debts, 1):
            if query in str(d['customer']).lower() or query in str(d['phone']).lower():
                debt_tree.insert("", "end", values=(idx, d['customer'], d['phone'], d['amount'], d['note']))
    search_frame = tk.Frame(frame, bg="#dff9fb")
    search_frame.pack(pady=(10,0))
    tk.Label(search_frame, text="بحث:", font=("Cairo", 12), bg="#dff9fb").pack(side="right")
    search_entry = tk.Entry(search_frame, textvariable=debt_search_var, font=("Cairo", 12), width=30)
    search_entry.pack(side="right", padx=5)
    tk.Button(search_frame, text="بحث", command=search_debts, bg="#487eb0", fg="white", font=("Cairo", 11)).pack(side="right", padx=5)
    debt_search_var.trace_add('write', search_debts)
    # جدول الديون
    debt_tree_frame = tk.Frame(frame, bg="#dff9fb")
    debt_tree_frame.pack(pady=20, fill="both", expand=1)
    debt_tree_scroll_y = tk.Scrollbar(debt_tree_frame, orient="vertical")
    debt_tree_scroll_y.pack(side="left", fill="y")
    debt_tree_scroll_x = tk.Scrollbar(debt_tree_frame, orient="horizontal")
    debt_tree_scroll_x.pack(side="bottom", fill="x")
    debt_columns = ("#", "اسم العميل", "الهاتف", "المبلغ", "ملاحظة")
    debt_tree = ttk.Treeview(debt_tree_frame, columns=debt_columns, show="headings", height=10,
                        yscrollcommand=debt_tree_scroll_y.set, xscrollcommand=debt_tree_scroll_x.set)
    for col in debt_columns:
        debt_tree.heading(col, text=col)
        debt_tree.column(col, anchor="center", width=120)
    debt_tree.pack(fill="both", expand=1)
    debt_tree_scroll_y.config(command=debt_tree.yview)
    debt_tree_scroll_x.config(command=debt_tree.xview)
    # دوال العمليات
    def refresh_debts():
        debts.clear()
        for item in debt_tree.get_children():
            values = debt_tree.item(item)['values']
            if len(values) == 5:
                debts.append({
                    'customer': str(values[1]),
                    'phone': str(values[2]),
                    'amount': float(values[3]),
                    'note': str(values[4])
                })
        save_data()
        search_debts()
    def add_debt():
        def save():
            customer = entry_customer.get()
            phone = entry_phone.get()
            amount = entry_amount.get()
            note = entry_note.get()
            if customer and phone and amount:
                debt_tree.insert("", "end", values=(len(debt_tree.get_children())+1, customer, phone, amount, note))
                add_win.destroy()
                refresh_debts()
                log_action("إضافة دين", f"{customer} - الهاتف: {phone} - المبلغ: {amount}")
        add_win = tk.Toplevel(root)
        add_win.title("إضافة دين")
        add_win.geometry("350x300")
        tk.Label(add_win, text="اسم العميل:").pack(pady=3)
        entry_customer = tk.Entry(add_win)
        entry_customer.pack()
        tk.Label(add_win, text="الهاتف:").pack(pady=3)
        entry_phone = tk.Entry(add_win)
        entry_phone.pack()
        tk.Label(add_win, text="المبلغ:").pack(pady=3)
        entry_amount = tk.Entry(add_win)
        entry_amount.pack()
        tk.Label(add_win, text="ملاحظة:").pack(pady=3)
        entry_note = tk.Entry(add_win)
        entry_note.pack()
        tk.Button(add_win, text="حفظ", command=save, bg="#44bd32", fg="white").pack(pady=10)
    def edit_debt():
        selected = debt_tree.selection()
        if not selected:
            return
        item = debt_tree.item(selected)
        values = item['values']
        def save():
            customer = entry_customer.get()
            phone = entry_phone.get()
            amount = entry_amount.get()
            note = entry_note.get()
            if customer and phone and amount:
                debt_tree.item(selected, values=(values[0], customer, phone, amount, note))
                edit_win.destroy()
                refresh_debts()
                log_action("تعديل دين", f"{customer} - الهاتف: {phone} - المبلغ: {amount}")
        edit_win = tk.Toplevel(root)
        edit_win.title("تعديل دين")
        edit_win.geometry("350x300")
        tk.Label(edit_win, text="اسم العميل:").pack(pady=3)
        entry_customer = tk.Entry(edit_win)
        entry_customer.insert(0, values[1])
        entry_customer.pack()
        tk.Label(edit_win, text="الهاتف:").pack(pady=3)
        entry_phone = tk.Entry(edit_win)
        entry_phone.insert(0, values[2])
        entry_phone.pack()
        tk.Label(edit_win, text="المبلغ:").pack(pady=3)
        entry_amount = tk.Entry(edit_win)
        entry_amount.insert(0, values[3])
        entry_amount.pack()
        tk.Label(edit_win, text="ملاحظة:").pack(pady=3)
        entry_note = tk.Entry(edit_win)
        entry_note.insert(0, values[4])
        entry_note.pack()
        tk.Button(edit_win, text="حفظ", command=save, bg="#e1b12c", fg="black").pack(pady=10)
    def delete_debt():
        selected = debt_tree.selection()
        if selected:
            debt_tree.delete(selected)
            refresh_debts()
            log_action("حذف دين", f"تم حذف الدين عن العميل: {selected}")
    # أزرار العمليات
    btn_frame = tk.Frame(frame, bg="#dff9fb")
    btn_frame.pack(pady=10)
    tk.Button(btn_frame, text="إضافة", command=add_debt, bg="#44bd32", fg="white", width=10).pack(side="right", padx=5)
    tk.Button(btn_frame, text="تعديل", command=edit_debt, bg="#e1b12c", fg="black", width=10).pack(side="right", padx=5)
    tk.Button(btn_frame, text="حذف", command=delete_debt, bg="#c23616", fg="white", width=10).pack(side="right", padx=5)
    # تحميل الديون في الجدول عند بدء التشغيل
    def load_debts_to_tree():
        debt_tree.delete(*debt_tree.get_children())
        for idx, d in enumerate(debts, 1):
            debt_tree.insert("", "end", values=(idx, d['customer'], d['phone'], d['amount'], d['note']))
    load_debts_to_tree()

# --- تبويب الإعدادات ---
if tab_names[2] == "الإعدادات":
    frame = frames[2]
    settings_title = tk.Label(frame, text="إعدادات البرنامج", font=("Cairo", 22, "bold"), bg="#dff9fb", fg="#273c75")
    settings_title.pack(pady=10)
    # إعدادات اسم المتجر
    store_name_var = tk.StringVar(value=title.cget("text"))
    def save_store_name():
        new_name = store_name_var.get().strip()
        if new_name:
            title.config(text=new_name)
            messagebox.showinfo("تم التحديث", "تم تحديث اسم المتجر في العنوان.")
    store_frame = tk.Frame(frame, bg="#dff9fb")
    store_frame.pack(pady=10)
    tk.Label(store_frame, text="اسم المتجر:", font=("Cairo", 14), bg="#dff9fb").pack(side="right", padx=5)
    store_entry = tk.Entry(store_frame, textvariable=store_name_var, font=("Cairo", 14), width=30)
    store_entry.pack(side="right", padx=5)
    tk.Button(store_frame, text="حفظ الاسم", command=save_store_name, bg="#487eb0", fg="white", font=("Cairo", 12)).pack(side="right", padx=5)
    # إعدادات ألوان الواجهة
    color_frame = tk.Frame(frame, bg="#dff9fb")
    color_frame.pack(pady=10)
    tk.Label(color_frame, text="لون خلفية البرنامج:", font=("Cairo", 14), bg="#dff9fb").pack(side="right", padx=5)
    def change_bg_color():
        color = colorchooser.askcolor(title="اختر لون الخلفية")[1]
        if color:
            root.configure(bg=color)
            for f in frames:
                f.configure(bg=color)
            main_title_frame.configure(bg=color)
            title.configure(bg=color)
            subtitle.configure(bg=color)
    tk.Button(color_frame, text="تغيير اللون", command=change_bg_color, bg="#e1b12c", fg="black", font=("Cairo", 12)).pack(side="right", padx=5)
    # إعدادات عامة أخرى (مثال: رسالة ترحيب)
    welcome_var = tk.StringVar(value="مرحباً بك في SalesMasterPro2025!")
    def save_welcome():
        messagebox.showinfo("تم الحفظ", f"تم حفظ الرسالة: {welcome_var.get()}")
    welcome_frame = tk.Frame(frame, bg="#dff9fb")
    welcome_frame.pack(pady=10)
    tk.Label(welcome_frame, text="رسالة ترحيب:", font=("Cairo", 14), bg="#dff9fb").pack(side="right", padx=5)
    welcome_entry = tk.Entry(welcome_frame, textvariable=welcome_var, font=("Cairo", 14), width=40)
    welcome_entry.pack(side="right", padx=5)
    tk.Button(welcome_frame, text="حفظ الرسالة", command=save_welcome, bg="#44bd32", fg="white", font=("Cairo", 12)).pack(side="right", padx=5)

# --- شاشة تسجيل الدخول ---
def show_login():
    login_win = tk.Toplevel(root)
    login_win.title("تسجيل الدخول")
    login_win.geometry("350x180")
    login_win.grab_set()
    login_win.resizable(False, False)
    tk.Label(login_win, text="اسم المستخدم:", font=("Cairo", 14)).pack(pady=10)
    username_entry = tk.Entry(login_win, font=("Cairo", 14))
    username_entry.pack()
    def do_login():
        root.deiconify()
        login_win.destroy()
    tk.Button(login_win, text="دخول", command=do_login, bg="#487eb0", fg="white", font=("Cairo", 14)).pack(pady=15)
    username_entry.focus()
    root.withdraw()
    login_win.protocol("WM_DELETE_WINDOW", root.destroy)

# استدعاء شاشة تسجيل الدخول عند بدء التشغيل
show_login()

# --- استدعاء الإشعارات عند بدء التشغيل وبعد كل عملية بيع أو حفظ أو استرجاع ---
root.after(1500, check_notifications)
# تحديث تقارير الربح بعد كل عملية بيع أو تحميل بيانات
if 'update_profit_stats' in globals():
    update_profit_stats()

# --- تشغيل واجهة البرنامج ---
root.mainloop()

# تحديث ملخصات الربح بعد البيع أو الحفظ أو استرجاع البيانات
# if 'update_profit_summaries' in globals():
#     update_profit_summaries()
